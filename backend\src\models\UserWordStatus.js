module.exports = (sequelize, DataTypes) => {
  const UserWordStatus = sequelize.define("UserWordStatus", {
    status_id: { 
      type: DataTypes.BIGINT.UNSIGNED, 
      primaryKey: true,
      autoIncrement: true
    },
    user_id: { type: DataTypes.BIGINT.UNSIGNED, allowNull: false },
    word_id: { type: DataTypes.BIGINT.UNSIGNED },
    user_word_id: { type: DataTypes.BIGINT.UNSIGNED },
    topic_id: { type: DataTypes.BIGINT.UNSIGNED, allowNull: false },
    learning_status: { 
      type: DataTypes.ENUM('not_started', 'learning', 'mastered', 'review'),
      defaultValue: 'not_started'
    },
    mastery_level: { type: DataTypes.INTEGER, defaultValue: 0 },
    is_marked_as_learned: { type: DataTypes.BOOLEAN, defaultValue: false },
    marked_at: { type: DataTypes.DATE },
    last_reviewed_at: { type: DataTypes.DATE },
    review_count: { type: DataTypes.INTEGER, defaultValue: 0 },
    correct_answers: { type: DataTypes.INTEGER, defaultValue: 0 },
    total_attempts: { type: DataTypes.INTEGER, defaultValue: 0 }
  }, {
    tableName: "user_word_status",
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });
  return UserWordStatus;
};