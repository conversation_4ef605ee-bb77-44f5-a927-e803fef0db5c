const { Sequelize } = require("sequelize");
require("dotenv").config();

/**
 * Script để chẩn đoán và sửa lỗi database datetime
 */

// Tạo kết nối database với các tùy chọn đặc biệt để xử lý datetime issues
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: "mysql",
    logging: console.log,
    dialectOptions: {
      dateStrings: true,
      typeCast: function (field, next) {
        if (field.type === 'DATETIME' || field.type === 'TIMESTAMP') {
          const value = field.string();
          if (value === '0000-00-00 00:00:00' || value === null) {
            return null;
          }
          return value;
        }
        return next();
      }
    },
    timezone: '+07:00'
  }
);

/**
 * Kiểm tra và sửa các bảng có datetime issues
 */
const fixDatabaseDatetimeIssues = async () => {
  try {
    console.log('🔍 Đang kiểm tra kết nối database...');

    // Test kết nối
    await sequelize.authenticate();
    console.log('✅ Kết nối database thành công!');

    // Lấy danh sách tất cả các bảng
    const [tables] = await sequelize.query(`
      SELECT TABLE_NAME
      FROM information_schema.tables
      WHERE table_schema = '${process.env.DB_NAME}'
    `);

    console.log(`📊 Tìm thấy ${tables.length} bảng trong database`);

    if (tables.length === 0) {
      console.log('ℹ️  Database trống, không cần sửa gì');
      return;
    }

    // Kiểm tra từng bảng có cột datetime
    for (const table of tables) {
      const tableName = table.TABLE_NAME;
      console.log(`\n🔍 Kiểm tra bảng: ${tableName}`);

      try {
        // Lấy thông tin cột của bảng
        const [columns] = await sequelize.query(`
          SELECT COLUMN_NAME, DATA_TYPE
          FROM information_schema.columns
          WHERE table_schema = '${process.env.DB_NAME}'
          AND table_name = '${tableName}'
          AND DATA_TYPE IN ('datetime', 'timestamp')
        `);

        if (columns.length > 0) {
          console.log(`   📅 Tìm thấy ${columns.length} cột datetime/timestamp`);

          // Kiểm tra và sửa từng cột datetime
          for (const column of columns) {
            const columnName = column.COLUMN_NAME;

            // Kiểm tra có record nào có giá trị '0000-00-00 00:00:00'
            const [invalidRecords] = await sequelize.query(`
              SELECT COUNT(*) as count
              FROM ${tableName}
              WHERE ${columnName} = '0000-00-00 00:00:00'
            `);

            const invalidCount = invalidRecords[0].count;

            if (invalidCount > 0) {
              console.log(`   ⚠️  Tìm thấy ${invalidCount} record có giá trị datetime không hợp lệ trong cột ${columnName}`);

              // Sửa các giá trị không hợp lệ thành NULL
              await sequelize.query(`
                UPDATE ${tableName}
                SET ${columnName} = NULL
                WHERE ${columnName} = '0000-00-00 00:00:00'
              `);

              console.log(`   ✅ Đã sửa ${invalidCount} record trong cột ${columnName}`);
            } else {
              console.log(`   ✅ Cột ${columnName} không có giá trị không hợp lệ`);
            }
          }
        }
      } catch (error) {
        console.log(`   ❌ Lỗi khi kiểm tra bảng ${tableName}: ${error.message}`);
      }
    }

    console.log('\n🎉 Hoàn thành kiểm tra và sửa database!');

  } catch (error) {
    console.error('❌ Lỗi khi sửa database:', error.message);
    throw error;
  } finally {
    await sequelize.close();
  }
};

/**
 * Kiểm tra và sửa các bảng có datetime issues với connection được truyền vào
 */
const fixDatabaseDatetimeIssuesWithConnection = async (connection) => {
  try {
    console.log('🔍 Đang kiểm tra database...');

    // Lấy danh sách tất cả các bảng
    const [tables] = await connection.query(`
      SELECT TABLE_NAME
      FROM information_schema.tables
      WHERE table_schema = '${process.env.DB_NAME}'
    `);

    console.log(`📊 Tìm thấy ${tables.length} bảng trong database`);

    if (tables.length === 0) {
      console.log('ℹ️  Database trống, không cần sửa gì');
      return;
    }

    // Kiểm tra từng bảng có cột datetime
    for (const table of tables) {
      const tableName = table.TABLE_NAME;
      console.log(`\n🔍 Kiểm tra bảng: ${tableName}`);

      try {
        // Lấy thông tin cột của bảng
        const [columns] = await connection.query(`
          SELECT COLUMN_NAME, DATA_TYPE
          FROM information_schema.columns
          WHERE table_schema = '${process.env.DB_NAME}'
          AND table_name = '${tableName}'
          AND DATA_TYPE IN ('datetime', 'timestamp')
        `);

        if (columns.length > 0) {
          console.log(`   📅 Tìm thấy ${columns.length} cột datetime/timestamp`);

          // Kiểm tra và sửa từng cột datetime
          for (const column of columns) {
            const columnName = column.COLUMN_NAME;

            // Kiểm tra có record nào có giá trị '0000-00-00 00:00:00'
            const [invalidRecords] = await connection.query(`
              SELECT COUNT(*) as count
              FROM ${tableName}
              WHERE ${columnName} = '0000-00-00 00:00:00'
            `);

            const invalidCount = invalidRecords[0].count;

            if (invalidCount > 0) {
              console.log(`   ⚠️  Tìm thấy ${invalidCount} record có giá trị datetime không hợp lệ trong cột ${columnName}`);

              // Sửa các giá trị không hợp lệ thành NULL
              await connection.query(`
                UPDATE ${tableName}
                SET ${columnName} = NULL
                WHERE ${columnName} = '0000-00-00 00:00:00'
              `);

              console.log(`   ✅ Đã sửa ${invalidCount} record trong cột ${columnName}`);
            } else {
              console.log(`   ✅ Cột ${columnName} không có giá trị không hợp lệ`);
            }
          }
        }
      } catch (error) {
        console.log(`   ❌ Lỗi khi kiểm tra bảng ${tableName}: ${error.message}`);
      }
    }

    console.log('\n🎉 Hoàn thành kiểm tra và sửa database!');

  } catch (error) {
    console.error('❌ Lỗi khi sửa database:', error.message);
    throw error;
  }
};

/**
 * Thiết lập lại SQL mode để tránh lỗi datetime
 */
const setSqlMode = async (connection) => {
  try {
    console.log('🔧 Đang thiết lập SQL mode...');

    // Thiết lập SQL mode để cho phép zero dates (bỏ NO_AUTO_CREATE_USER vì không còn hỗ trợ trong MySQL 8.0+)
    await connection.query(`
      SET SESSION sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION'
    `);

    console.log('✅ Đã thiết lập SQL mode thành công');

  } catch (error) {
    console.error('❌ Lỗi khi thiết lập SQL mode:', error.message);
    throw error;
  }
};

// Chạy script
const main = async () => {
  console.log('🚀 BẮT ĐẦU SỬA LỖI DATABASE DATETIME');
  console.log('='.repeat(50));

  let connection = null;

  try {
    // Tạo kết nối mới cho toàn bộ quá trình
    connection = new Sequelize(
      process.env.DB_NAME,
      process.env.DB_USER,
      process.env.DB_PASSWORD,
      {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        dialect: "mysql",
        logging: false, // Tắt logging để output sạch hơn
        dialectOptions: {
          dateStrings: true,
          typeCast: function (field, next) {
            if (field.type === 'DATETIME' || field.type === 'TIMESTAMP') {
              const value = field.string();
              if (value === '0000-00-00 00:00:00' || value === null) {
                return null;
              }
              return value;
            }
            return next();
          }
        },
        timezone: '+07:00'
      }
    );

    await connection.authenticate();
    console.log('✅ Kết nối database thành công!');

    // Bước 1: Thiết lập SQL mode
    await setSqlMode(connection);

    // Bước 2: Sửa các giá trị datetime không hợp lệ
    await fixDatabaseDatetimeIssuesWithConnection(connection);

    console.log('\n✅ HOÀN THÀNH! Database đã được sửa.');
    console.log('💡 Bây giờ bạn có thể khởi động lại server.');

  } catch (error) {
    console.error('\n❌ THẤT BẠI! Không thể sửa database:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.close();
    }
  }
};

// Chạy script nếu được gọi trực tiếp
if (require.main === module) {
  main();
}

module.exports = {
  fixDatabaseDatetimeIssues,
  setSqlMode
};
