// Main App Component
import React from "react";
import { <PERSON>rowser<PERSON>outer as Router } from "react-router-dom";
import { Provider } from "react-redux";
import { store } from "./redux/store";
import AppRoutes from "./routes/routes";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "./assets/styles/global.css"; // CSS tùy chỉnh
// Import global styles
import "./assets/styles/reset.css";
import "./assets/styles/global.css";
import "./App.css";
import CoursePreview from "./Client/pages/Course/CoursePreview/CoursePreview";

function App() {
  return (
    <Provider store={store}>
      <Router>
        <div className="App">
          <AppRoutes />
        </div>
      </Router>
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </Provider>
  );
}

export default App;
