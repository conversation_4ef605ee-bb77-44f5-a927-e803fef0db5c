/* Header Component - CSS theo chuẩn BEM */

.header {
  background: linear-gradient(135deg, #4fd1c7 0%, #38b2ac 100%);
  padding: 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2px 20px rgba(79, 209, 199, 0.2);
}

.header__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

/* Logo */
.header__logo {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.header__logo:hover {
  transform: scale(1.05);
}

.header__logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.header__logo-text {
  font-size: 24px;
  font-weight: 700;
  color: white;
  letter-spacing: 1px;
}

/* Navigation */
.header__nav {
  display: flex;
  align-items: center;
  background-color: #47C7BE;
}

.header__nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 32px;
  background-color: #47C7BE;
}

.header__nav-item {
  position: relative;
  background-color: #47C7BE;
}

.header__nav-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  padding: 8px 0;
  transition: all 0.3s ease;
  position: relative;
}

.header__nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: white;
  transition: width 0.3s ease;
}

.header__nav-link:hover::after,
.header__nav-link--active::after {
  width: 100%;
}

.header__nav-link:hover {
  color: rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

/* Auth Buttons */
.header__auth {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header__btn {
  padding: 10px 24px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.header__btn--login {
  background: white;
  color: #4fd1c7;
  border-color: white;
}

.header__btn--login:hover {
  background: transparent;
  color: white;
  border-color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
}

.header__btn--signup {
  background: transparent;
  color: white;
  border-color: white;
}

.header__btn--signup:hover {
  background: white;
  color: #4fd1c7;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
}

/* User Menu */
.header__user-menu {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header__user-name {
  color: white;
  font-weight: 500;
  font-size: 14px;
}

.header__logout-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.header__logout-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* Mobile Menu Toggle */
.header__menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 30px;
  height: 25px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
}

.header__menu-toggle span {
  width: 100%;
  height: 3px;
  background: white;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.header__menu-toggle--open span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.header__menu-toggle--open span:nth-child(2) {
  opacity: 0;
}

.header__menu-toggle--open span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Client Layout */
.client-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.client-main {
  flex: 1;
  padding-top: 70px; /* Space for fixed header */
}

/* Responsive Design */
@media (max-width: 768px) {
  .header__container {
    padding: 0 16px;
  }

  .header__nav {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #4fd1c7 0%, #38b2ac 100%);
    padding: 20px;
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .header__nav--open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .header__nav-list {
    flex-direction: column;
    gap: 16px;
  }

  .header__nav-link {
    display: block;
    padding: 12px 0;
    font-size: 18px;
  }

  .header__menu-toggle {
    display: flex;
  }

  .header__auth {
    gap: 8px;
  }

  .header__btn {
    padding: 8px 16px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .header__logo-text {
    font-size: 20px;
  }

  .header__logo-icon {
    width: 28px;
    height: 28px;
  }

  .header__auth {
    flex-direction: column;
    gap: 8px;
  }

  .header__btn {
    padding: 6px 12px;
    font-size: 11px;
  }
}
