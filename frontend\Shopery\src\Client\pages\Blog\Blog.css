.blog-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 2rem 1rem;
}

.blog-container {
  max-width: 1200px;
  margin: 0 auto;
}

.blog-header {
  text-align: center;
  margin-bottom: 3rem;
}

.blog-header h1 {
  font-size: 3rem;
  color: #333;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.blog-header p {
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

/* Category Filter */
.blog-categories {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.blog-category-btn {
  padding: 0.8rem 1.5rem;
  border: 2px solid #667eea;
  background: white;
  color: #667eea;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.blog-category-btn:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

.blog-category-btn.active {
  background: #667eea;
  color: white;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.blog-category-count {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Blog Posts Grid */
.blog-posts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.blog-post {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-post:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.blog-post-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.blog-post-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.blog-post:hover .blog-post-image img {
  transform: scale(1.05);
}

.blog-post-category {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: rgba(102, 126, 234, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.blog-post-content {
  padding: 1.5rem;
}

.blog-post-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: #666;
  flex-wrap: wrap;
}

.blog-post-author {
  color: #667eea;
  font-weight: 600;
}

.blog-post-title {
  font-size: 1.3rem;
  color: #333;
  margin-bottom: 1rem;
  line-height: 1.4;
  font-weight: 600;
}

.blog-post-excerpt {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.blog-post-read-more {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.blog-post-read-more:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Load More Button */
.blog-load-more {
  text-align: center;
}

.blog-load-more-btn {
  background: white;
  border: 2px solid #667eea;
  color: #667eea;
  padding: 1rem 2rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.blog-load-more-btn:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .blog-header h1 {
    font-size: 2rem;
  }
  
  .blog-header p {
    font-size: 1rem;
  }
  
  .blog-categories {
    justify-content: center;
  }
  
  .blog-category-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
  
  .blog-posts {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .blog-post-content {
    padding: 1rem;
  }
  
  .blog-post-title {
    font-size: 1.1rem;
  }
}
